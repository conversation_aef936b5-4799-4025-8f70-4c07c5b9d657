#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索框双击清空功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加V198目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_search_double_click():
    """测试搜索框双击清空功能"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("搜索框双击清空功能测试")
    root.geometry("400x200")
    
    # 创建说明标签
    info_label = tk.Label(root, text="在搜索框中输入内容，然后双击测试清空功能", 
                         font=('Arial', 12), pady=10)
    info_label.pack()
    
    # 创建搜索框框架
    search_frame = ttk.Frame(root)
    search_frame.pack(fill=tk.X, padx=20, pady=10)
    
    # 创建搜索变量和输入框
    search_var = tk.StringVar()
    search_entry = ttk.Entry(search_frame, textvariable=search_var, font=('Arial', 12))
    search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    # 状态显示标签
    status_label = tk.Label(root, text="状态：等待测试", font=('Arial', 10), fg='blue')
    status_label.pack(pady=10)
    
    def clear_search_on_double_click(event):
        """双击搜索框时自动清空内容"""
        try:
            # 清空搜索框内容
            search_var.set("")
            # 将光标移到开头
            search_entry.icursor(0)
            # 更新状态
            status_label.config(text="状态：搜索框已清空", fg='green')
            print("搜索框已清空")
        except Exception as e:
            status_label.config(text=f"状态：清空失败 - {e}", fg='red')
            print(f"清空搜索框失败: {e}")
    
    def on_content_change(*args):
        """搜索框内容变化时更新状态"""
        content = search_var.get()
        if content:
            status_label.config(text=f"状态：当前内容 - '{content}'", fg='black')
        else:
            status_label.config(text="状态：搜索框为空", fg='gray')
    
    # 绑定双击事件
    search_entry.bind('<Double-Button-1>', clear_search_on_double_click)
    
    # 绑定内容变化事件
    search_var.trace_add("write", on_content_change)
    
    # 添加一些测试内容
    search_var.set("测试内容 - 双击清空")
    
    # 创建测试按钮
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    def add_test_content():
        """添加测试内容"""
        search_var.set("这是测试内容，请双击清空")
        status_label.config(text="状态：已添加测试内容，请双击搜索框", fg='blue')
    
    def manual_clear():
        """手动清空"""
        search_var.set("")
        status_label.config(text="状态：手动清空完成", fg='orange')
    
    tk.Button(button_frame, text="添加测试内容", command=add_test_content).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="手动清空", command=manual_clear).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="退出", command=root.destroy).pack(side=tk.LEFT, padx=5)
    
    # 创建使用说明
    instruction_text = """
使用说明：
1. 在搜索框中输入一些内容
2. 双击搜索框，内容应该自动清空
3. 观察状态标签的变化
4. 可以使用"添加测试内容"按钮快速添加内容进行测试
    """
    
    instruction_label = tk.Label(root, text=instruction_text, font=('Arial', 9), 
                                justify=tk.LEFT, fg='gray')
    instruction_label.pack(pady=10)
    
    # 运行测试窗口
    print("搜索框双击清空功能测试窗口已启动")
    print("请在搜索框中输入内容，然后双击测试清空功能")
    
    root.mainloop()

if __name__ == "__main__":
    test_search_double_click()
