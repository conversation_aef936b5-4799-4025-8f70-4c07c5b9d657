#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索图标显示效果
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加V198目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_search_icon():
    """测试搜索图标显示效果"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("搜索图标显示测试")
    root.geometry("500x300")
    
    # 创建说明标签
    info_label = tk.Label(root, text="测试搜索框和搜索图标的显示效果", 
                         font=('Arial', 12), pady=10)
    info_label.pack()
    
    # 创建搜索框框架
    search_frame = ttk.Frame(root)
    search_frame.pack(fill=tk.X, padx=20, pady=10)
    
    # 创建搜索变量和输入框
    search_var = tk.StringVar()
    search_entry = ttk.Entry(search_frame, textvariable=search_var, font=('Arial', 12))
    search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    # 添加搜索图标
    search_icon = ttk.Label(search_frame, text="🔍", font=('Arial', 16))
    search_icon.pack(side=tk.LEFT, padx=5)
    
    # 状态显示标签
    status_label = tk.Label(root, text="状态：等待输入", font=('Arial', 10), fg='blue')
    status_label.pack(pady=10)
    
    def clear_search_on_double_click(event):
        """双击搜索框时自动清空内容"""
        try:
            # 清空搜索框内容
            search_var.set("")
            # 将光标移到开头
            search_entry.icursor(0)
            # 更新状态
            status_label.config(text="状态：搜索框已清空", fg='green')
            print("搜索框已清空")
        except Exception as e:
            status_label.config(text=f"状态：清空失败 - {e}", fg='red')
            print(f"清空搜索框失败: {e}")
    
    def on_content_change(*args):
        """搜索框内容变化时更新状态"""
        content = search_var.get()
        if content:
            status_label.config(text=f"状态：正在搜索 - '{content}'", fg='black')
        else:
            status_label.config(text="状态：搜索框为空", fg='gray')
    
    # 绑定双击事件
    search_entry.bind('<Double-Button-1>', clear_search_on_double_click)
    
    # 绑定内容变化事件
    search_var.trace_add("write", on_content_change)
    
    # 创建对比示例
    comparison_frame = tk.LabelFrame(root, text="对比效果", font=('Arial', 10))
    comparison_frame.pack(fill=tk.X, padx=20, pady=10)
    
    # 原来的按钮样式
    old_frame = ttk.Frame(comparison_frame)
    old_frame.pack(fill=tk.X, pady=5)
    tk.Label(old_frame, text="原来的样式:", font=('Arial', 10)).pack(side=tk.LEFT)
    ttk.Entry(old_frame, width=30).pack(side=tk.LEFT, padx=5)
    ttk.Button(old_frame, text="搜索").pack(side=tk.LEFT, padx=5)
    
    # 新的图标样式
    new_frame = ttk.Frame(comparison_frame)
    new_frame.pack(fill=tk.X, pady=5)
    tk.Label(new_frame, text="新的样式:", font=('Arial', 10)).pack(side=tk.LEFT)
    ttk.Entry(new_frame, width=30).pack(side=tk.LEFT, padx=5)
    ttk.Label(new_frame, text="🔍", font=('Arial', 16)).pack(side=tk.LEFT, padx=5)
    
    # 创建测试按钮
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    def add_test_content():
        """添加测试内容"""
        search_var.set("测试搜索内容")
        status_label.config(text="状态：已添加测试内容", fg='blue')
    
    def test_different_icons():
        """测试不同的图标"""
        icons = ["🔍", "🔎", "⌕", "🔍︎"]
        current_icon = search_icon.cget("text")
        try:
            current_index = icons.index(current_icon)
            next_index = (current_index + 1) % len(icons)
        except ValueError:
            next_index = 0
        
        search_icon.config(text=icons[next_index])
        status_label.config(text=f"状态：切换到图标 '{icons[next_index]}'", fg='purple')
    
    tk.Button(button_frame, text="添加测试内容", command=add_test_content).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="切换图标", command=test_different_icons).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="退出", command=root.destroy).pack(side=tk.LEFT, padx=5)
    
    # 创建使用说明
    instruction_text = """
使用说明：
1. 在搜索框中输入内容，观察实时搜索效果
2. 双击搜索框可以清空内容
3. 对比新旧样式的区别
4. 可以切换不同的搜索图标
5. 新样式更简洁，去掉了不必要的按钮
    """
    
    instruction_label = tk.Label(root, text=instruction_text, font=('Arial', 9), 
                                justify=tk.LEFT, fg='gray')
    instruction_label.pack(pady=10)
    
    # 运行测试窗口
    print("搜索图标显示测试窗口已启动")
    print("对比新旧样式，新样式使用图标替代按钮")
    
    root.mainloop()

if __name__ == "__main__":
    test_search_icon()
