#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义项目功能测试脚本
演示项目列表分为上下两部分的新功能
"""

import tkinter as tk
from tkinter import ttk
import sqlite3
from pathlib import Path

class CustomProjectsDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("自定义项目功能演示")
        self.root.geometry("800x600")
        
        # 设置字体
        self.default_font = 'Microsoft YaHei UI'
        
        self.create_ui()
        self.load_demo_data()
    
    def create_ui(self):
        """创建演示界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="V198 自定义项目功能演示", 
                               font=(self.default_font, 16, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # 说明文字
        desc_label = ttk.Label(main_frame, 
                              text="项目列表现在分为两部分：上半部分为原有项目，下半部分为自定义项目",
                              font=(self.default_font, 10))
        desc_label.pack(pady=(0, 10))
        
        # 创建项目列表区域
        self.create_project_lists(main_frame)
        
        # 操作说明
        self.create_instructions(main_frame)
    
    def create_project_lists(self, parent):
        """创建项目列表"""
        # 项目列表容器
        projects_frame = ttk.Frame(parent)
        projects_frame.pack(fill=tk.BOTH, expand=True)
        
        # 原有项目区域（上半部分）
        main_projects_frame = ttk.LabelFrame(projects_frame, text="项目")
        main_projects_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 原有项目树
        self.main_tree = ttk.Treeview(main_projects_frame, selectmode='browse', height=8)
        self.main_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        main_scrollbar = ttk.Scrollbar(main_projects_frame, orient=tk.VERTICAL, command=self.main_tree.yview)
        main_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.main_tree.configure(yscrollcommand=main_scrollbar.set)
        
        # 配置主项目树
        self.main_tree["columns"] = ("name",)
        self.main_tree.column("#0", width=0, stretch=tk.NO)
        self.main_tree.column("name", width=300, anchor=tk.W)
        self.main_tree.heading("#0", text="")
        self.main_tree.heading("name", text="项目名称")
        
        # 自定义项目区域（下半部分）
        custom_projects_frame = ttk.LabelFrame(projects_frame, text="自定义项目")
        custom_projects_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 自定义项目树
        self.custom_tree = ttk.Treeview(custom_projects_frame, selectmode='browse', height=8)
        self.custom_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        custom_scrollbar = ttk.Scrollbar(custom_projects_frame, orient=tk.VERTICAL, command=self.custom_tree.yview)
        custom_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.custom_tree.configure(yscrollcommand=custom_scrollbar.set)
        
        # 配置自定义项目树
        self.custom_tree["columns"] = ("name",)
        self.custom_tree.column("#0", width=0, stretch=tk.NO)
        self.custom_tree.column("name", width=300, anchor=tk.W)
        self.custom_tree.heading("#0", text="")
        self.custom_tree.heading("name", text="自定义项目")
        
        # 绑定事件
        self.main_tree.bind('<Double-1>', self.on_main_double_click)
        self.custom_tree.bind('<Double-1>', self.on_custom_double_click)
        self.main_tree.bind('<Button-3>', self.show_main_context_menu)
        self.custom_tree.bind('<Button-3>', self.show_custom_context_menu)
        
        # 创建右键菜单
        self.main_context_menu = tk.Menu(self.root, tearoff=0)
        self.custom_context_menu = tk.Menu(self.root, tearoff=0)
    
    def create_instructions(self, parent):
        """创建操作说明"""
        instructions_frame = ttk.LabelFrame(parent, text="功能说明")
        instructions_frame.pack(fill=tk.X, pady=(10, 0))
        
        instructions = [
            "• 右键点击上方项目可以选择'添加到自定义项目'",
            "• 双击项目可以模拟加载图片",
            "• 自定义项目支持拖拽排序（上移/下移）",
            "• 右键点击自定义项目可以移除或调整顺序",
            "• 自定义项目会保存常用的图片和文件夹"
        ]
        
        for i, instruction in enumerate(instructions):
            label = ttk.Label(instructions_frame, text=instruction, 
                             font=(self.default_font, 9))
            label.pack(anchor=tk.W, padx=10, pady=2)
    
    def load_demo_data(self):
        """加载演示数据"""
        # 模拟原有项目数据
        main_projects = [
            ("folder_1", "📁 ★ 赞美诗歌"),
            ("folder_2", "📁 ☆ 流行歌曲"),
            ("folder_3", "📁 ★ 🔢 经典老歌"),
            ("1", "🖼️ 主祷文.jpg"),
            ("2", "🖼️ 阿们颂.jpg"),
            ("3", "🖼️ 哈利路亚.jpg"),
        ]
        
        for item_id, name in main_projects:
            self.main_tree.insert("", "end", iid=item_id, text="", values=(name,))
        
        # 模拟自定义项目数据
        custom_projects = [
            ("custom_folder_1", "📁 赞美诗歌"),
            ("custom_image_1", "🖼️ 主祷文.jpg"),
            ("custom_image_3", "🖼️ 哈利路亚.jpg"),
            ("invalid_1", "❌ 已删除的歌曲 (已删除)"),
        ]
        
        for item_id, name in custom_projects:
            self.custom_tree.insert("", "end", iid=item_id, text="", values=(name,))
    
    def on_main_double_click(self, event):
        """主项目双击事件"""
        selected = self.main_tree.selection()
        if selected:
            item_id = selected[0]
            name = self.main_tree.item(item_id, "values")[0]
            print(f"模拟加载项目: {name}")
    
    def on_custom_double_click(self, event):
        """自定义项目双击事件"""
        selected = self.custom_tree.selection()
        if selected:
            item_id = selected[0]
            name = self.custom_tree.item(item_id, "values")[0]
            if item_id.startswith('invalid_'):
                print(f"项目已删除: {name}")
            else:
                print(f"模拟加载自定义项目: {name}")
    
    def show_main_context_menu(self, event):
        """显示主项目右键菜单"""
        item = self.main_tree.identify_row(event.y)
        if not item:
            return
        
        self.main_tree.selection_set(item)
        
        # 清除菜单
        self.main_context_menu.delete(0, "end")
        
        # 添加菜单项
        self.main_context_menu.add_command(
            label="添加到自定义项目",
            command=lambda: self.add_to_custom(item)
        )
        
        # 显示菜单
        self.main_context_menu.post(event.x_root, event.y_root)
    
    def show_custom_context_menu(self, event):
        """显示自定义项目右键菜单"""
        item = self.custom_tree.identify_row(event.y)
        if not item:
            return
        
        self.custom_tree.selection_set(item)
        
        # 清除菜单
        self.custom_context_menu.delete(0, "end")
        
        # 添加菜单项
        self.custom_context_menu.add_command(
            label="从自定义项目中移除",
            command=lambda: self.remove_from_custom(item)
        )
        
        if not item.startswith('invalid_'):
            self.custom_context_menu.add_separator()
            self.custom_context_menu.add_command(
                label="上移",
                command=lambda: self.move_custom_up(item)
            )
            self.custom_context_menu.add_command(
                label="下移",
                command=lambda: self.move_custom_down(item)
            )
        
        # 显示菜单
        self.custom_context_menu.post(event.x_root, event.y_root)
    
    def add_to_custom(self, item_id):
        """添加到自定义项目"""
        name = self.main_tree.item(item_id, "values")[0]
        
        # 检查是否已存在
        for child in self.custom_tree.get_children():
            if child == f"custom_{item_id}":
                print(f"项目 '{name}' 已在自定义项目中")
                return
        
        # 添加到自定义项目
        custom_id = f"custom_{item_id}"
        self.custom_tree.insert("", "end", iid=custom_id, text="", values=(name,))
        print(f"已将 '{name}' 添加到自定义项目")
    
    def remove_from_custom(self, item_id):
        """从自定义项目中移除"""
        name = self.custom_tree.item(item_id, "values")[0]
        self.custom_tree.delete(item_id)
        print(f"已将 '{name}' 从自定义项目中移除")
    
    def move_custom_up(self, item_id):
        """上移自定义项目"""
        children = self.custom_tree.get_children()
        index = children.index(item_id)
        if index > 0:
            self.custom_tree.move(item_id, "", index - 1)
            print(f"已上移项目")
    
    def move_custom_down(self, item_id):
        """下移自定义项目"""
        children = self.custom_tree.get_children()
        index = children.index(item_id)
        if index < len(children) - 1:
            self.custom_tree.move(item_id, "", index + 1)
            print(f"已下移项目")
    
    def run(self):
        """运行演示"""
        print("自定义项目功能演示启动")
        print("=" * 50)
        self.root.mainloop()

if __name__ == "__main__":
    demo = CustomProjectsDemo()
    demo.run()
