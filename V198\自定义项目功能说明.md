# V198 自定义项目功能说明

## 功能概述

V198 图像投影控制应用现在支持自定义项目功能，将项目列表分为两个独立的区域：

- **上半部分**：原有的项目列表（文件夹和图片）
- **下半部分**：自定义项目列表（用户收藏的常用项目）

## 主要特性

### 1. 双区域项目列表

项目界面现在使用 `ttk.LabelFrame` 分为两个独立的区域：

- **项目区域**：显示所有扫描到的文件夹和独立图片
- **自定义项目区域**：显示用户添加的常用项目集合

### 2. 添加到自定义项目

用户可以通过以下方式将项目添加到自定义列表：

1. 右键点击主项目列表中的任意项目（文件夹或图片）
2. 选择"添加到自定义项目"菜单项
3. 系统会自动将该项目添加到自定义项目列表的末尾

**特性**：
- 自动检测重复项目，避免重复添加
- 支持添加文件夹和单独的图片
- 保持原始项目的名称和图标

### 3. 自定义项目管理

自定义项目支持完整的管理功能：

#### 移除项目
- 右键点击自定义项目
- 选择"从自定义项目中移除"
- 确认后从自定义列表中删除

#### 调整顺序
- **上移/下移**：通过右键菜单选择"上移"或"下移"
- **拖拽排序**：直接拖拽项目到目标位置进行重新排序

### 4. 拖拽排序功能

自定义项目支持直观的拖拽排序：

1. **开始拖拽**：按住鼠标左键在项目上
2. **拖拽过程**：移动鼠标，目标位置会显示"→"提示符
3. **完成排序**：释放鼠标，项目会移动到新位置

**拖拽特性**：
- 实时视觉反馈（目标位置高亮）
- 自动清理临时标记
- 支持任意位置间的移动

### 5. 数据持久化

自定义项目数据存储在 SQLite 数据库中：

```sql
CREATE TABLE custom_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    item_type TEXT NOT NULL CHECK (item_type IN ('folder', 'image')),
    item_id INTEGER NOT NULL,
    order_index INTEGER NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(item_type, item_id)
);
```

**数据特性**：
- 支持文件夹和图片两种类型
- 维护排序索引确保顺序一致性
- 防止重复添加相同项目
- 自动处理已删除的原始文件

### 6. 错误处理

系统具备完善的错误处理机制：

- **无效项目检测**：自动识别已删除的原始文件
- **数据库异常处理**：确保数据操作的安全性
- **用户友好提示**：清晰的成功/错误消息

## 使用场景

### 典型工作流程

1. **项目扫描**：系统扫描指定目录，在上方显示所有项目
2. **收藏常用项目**：右键添加常用的歌曲/图片到自定义项目
3. **快速访问**：双击自定义项目直接加载，无需在大量项目中查找
4. **个性化排序**：根据使用频率调整自定义项目的顺序

### 适用场景

- **教会服务**：快速访问常用的赞美诗歌和经文图片
- **演出活动**：预设常用的背景图片和歌曲列表
- **教学环境**：收藏经常使用的教学素材

## 技术实现

### 核心组件

1. **UI 布局**：使用 `ttk.LabelFrame` 和 `ttk.Treeview` 实现双区域界面
2. **数据库层**：SQLite 存储自定义项目配置
3. **事件处理**：完整的鼠标事件处理（点击、拖拽、右键菜单）
4. **数据同步**：实时更新界面和数据库状态

### 关键方法

- `load_custom_projects()`: 加载自定义项目列表
- `add_to_custom_projects()`: 添加项目到自定义列表
- `remove_from_custom_projects()`: 从自定义列表移除项目
- `reorder_custom_projects()`: 拖拽排序实现
- `on_custom_project_select()`: 自定义项目选择处理

## 兼容性

- 完全向后兼容现有功能
- 不影响原有的项目扫描和显示逻辑
- 自定义项目为可选功能，不使用时不影响性能

## 测试

运行测试脚本验证功能：

```bash
python V198/test_custom_projects.py
```

测试脚本演示了完整的自定义项目功能，包括添加、移除、排序等操作。

---

*该功能极大提升了用户体验，特别适合需要频繁访问特定项目的使用场景。*
