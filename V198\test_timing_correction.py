#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试播放时间实时修正功能
"""

import sys
import os
import time
import sqlite3

# 添加V198目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from keytime import KeyTimeRecorder, AutoPlayer

def test_timing_correction():
    """测试时间修正功能"""
    print("开始测试播放时间实时修正功能...")
    
    # 创建测试数据库
    db_path = "test_timing_correction.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # 初始化时间记录器
    time_recorder = KeyTimeRecorder(db_path)
    
    # 创建测试数据
    test_image_id = 1
    test_keyframes = [
        (1, 0.1),  # 关键帧1，位置0.1
        (2, 0.3),  # 关键帧2，位置0.3
        (3, 0.5),  # 关键帧3，位置0.5
    ]
    
    # 插入测试关键帧
    with sqlite3.connect(db_path) as conn:
        # 创建关键帧表
        conn.execute('''
            CREATE TABLE IF NOT EXISTS keyframes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                image_id INTEGER NOT NULL,
                position REAL NOT NULL,
                order_index INTEGER NOT NULL
            )
        ''')
        
        # 插入测试关键帧
        for i, (kf_id, position) in enumerate(test_keyframes):
            conn.execute('''
                INSERT INTO keyframes (id, image_id, position, order_index)
                VALUES (?, ?, ?, ?)
            ''', (kf_id, test_image_id, position, i))
        
        conn.commit()
    
    # 开始录制时间
    time_recorder.start_recording(test_image_id)
    
    # 模拟录制过程
    print("模拟录制关键帧时间...")
    for i, (kf_id, _) in enumerate(test_keyframes):
        time.sleep(1.0)  # 模拟停留1秒
        time_recorder.record_keyframe_timing(kf_id)
        print(f"录制关键帧 {kf_id} 时间: 1.0秒")
    
    # 停止录制
    time_recorder.stop_recording()
    
    # 获取录制的时间序列
    timing_sequence = time_recorder.get_timing_sequence(test_image_id)
    print(f"\n原始录制的时间序列: {timing_sequence}")
    
    # 测试时间修正功能
    print("\n测试时间修正功能...")
    
    # 修正第一个关键帧的时间为2.5秒
    new_duration = 2.5
    first_keyframe_id = test_keyframes[0][0]
    
    success = time_recorder.update_keyframe_timing_in_db(test_image_id, first_keyframe_id, new_duration)
    if success:
        print(f"成功修正关键帧 {first_keyframe_id} 的时间为 {new_duration}秒")
    else:
        print(f"修正关键帧 {first_keyframe_id} 的时间失败")
    
    # 获取修正后的时间序列
    updated_timing_sequence = time_recorder.get_timing_sequence(test_image_id)
    print(f"修正后的时间序列: {updated_timing_sequence}")
    
    # 验证修正是否成功
    if updated_timing_sequence:
        first_timing = updated_timing_sequence[0]
        if abs(first_timing[1] - new_duration) < 0.01:  # 允许小误差
            print("✓ 时间修正功能测试通过")
        else:
            print("✗ 时间修正功能测试失败")
    
    # 清理测试文件
    try:
        if os.path.exists(db_path):
            os.remove(db_path)
    except PermissionError:
        print(f"注意：无法删除测试文件 {db_path}，请手动删除")
    
    print("测试完成！")

def test_original_mode_timing_correction():
    """测试原图模式时间修正功能"""
    print("\n开始测试原图模式播放时间实时修正功能...")
    
    # 创建测试数据库
    db_path = "test_original_timing_correction.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # 初始化时间记录器
    time_recorder = KeyTimeRecorder(db_path)
    
    # 创建测试数据
    base_image_id = 1
    test_transitions = [
        (1, 2),  # 图片1 -> 图片2
        (2, 3),  # 图片2 -> 图片3
        (3, 1),  # 图片3 -> 图片1
    ]
    
    # 开始原图模式录制
    time_recorder.start_original_mode_recording(base_image_id)
    
    # 模拟录制过程
    print("模拟录制原图模式时间...")
    for from_id, to_id in test_transitions:
        time.sleep(1.5)  # 模拟停留1.5秒
        time_recorder.record_original_mode_timing(from_id, to_id)
        print(f"录制原图模式时间 {from_id} -> {to_id}: 1.5秒")
    
    # 停止录制
    time_recorder.stop_original_mode_recording()
    
    # 获取录制的时间序列
    timing_sequence = time_recorder.get_original_mode_timing_sequence(base_image_id)
    print(f"\n原始录制的原图模式时间序列: {timing_sequence}")
    
    # 测试原图模式时间修正功能
    print("\n测试原图模式时间修正功能...")
    
    # 修正第一个转换的时间为3.0秒
    new_duration = 3.0
    from_id, to_id = test_transitions[0]
    
    success = time_recorder.update_original_mode_timing_in_db(base_image_id, from_id, to_id, new_duration)
    if success:
        print(f"成功修正原图模式时间 {from_id} -> {to_id} 为 {new_duration}秒")
    else:
        print(f"修正原图模式时间 {from_id} -> {to_id} 失败")
    
    # 获取修正后的时间序列
    updated_timing_sequence = time_recorder.get_original_mode_timing_sequence(base_image_id)
    print(f"修正后的原图模式时间序列: {updated_timing_sequence}")
    
    # 验证修正是否成功
    if updated_timing_sequence:
        first_timing = updated_timing_sequence[0]
        if abs(first_timing[2] - new_duration) < 0.01:  # 允许小误差
            print("✓ 原图模式时间修正功能测试通过")
        else:
            print("✗ 原图模式时间修正功能测试失败")
    
    # 清理测试文件
    try:
        if os.path.exists(db_path):
            os.remove(db_path)
    except PermissionError:
        print(f"注意：无法删除测试文件 {db_path}，请手动删除")
    
    print("原图模式测试完成！")

if __name__ == "__main__":
    test_timing_correction()
    test_original_mode_timing_correction()
    print("\n所有测试完成！")
